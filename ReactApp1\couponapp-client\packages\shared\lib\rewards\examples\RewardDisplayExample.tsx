import React from 'react';
import { RewardDisplayProvider, useRewardDisplay, DisplayableReward } from '../index';

// Example component that displays the current reward
const RewardImage: React.FC = () => {
  const { currentReward, isVisible } = useRewardDisplay();

  if (!isVisible || !currentReward) {
    return null;
  }

  return (
    <div className="reward-display">
      {currentReward.image?.absoluteUrl && (
        <img 
          src={currentReward.image.absoluteUrl} 
          alt={currentReward.name || 'Reward'} 
          className="w-24 h-24 object-cover rounded"
        />
      )}
      {currentReward.name && (
        <h3 className="text-lg font-semibold">{currentReward.name}</h3>
      )}
      {currentReward.description && (
        <p className="text-sm text-gray-600">{currentReward.description}</p>
      )}
    </div>
  );
};

// Example component that controls the reward display
const RewardController: React.FC = () => {
  const { setCurrentReward, setIsVisible, clearReward } = useRewardDisplay();

  const sampleReward: DisplayableReward = {
    id: 'sample-reward',
    name: '50% Off Coupon',
    description: 'Get 50% off your next purchase',
    image: {
      absoluteUrl: 'https://placehold.co/100x100/00ff00/ffffff?text=50OFF'
    },
    type: 'coupon-code'
  };

  const showReward = () => {
    setCurrentReward(sampleReward);
    setIsVisible(true);
  };

  const hideReward = () => {
    setIsVisible(false);
  };

  return (
    <div className="space-x-2">
      <button 
        onClick={showReward}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Show Reward
      </button>
      <button 
        onClick={hideReward}
        className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
      >
        Hide Reward
      </button>
      <button 
        onClick={clearReward}
        className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
      >
        Clear Reward
      </button>
    </div>
  );
};

// Example usage of the RewardDisplayProvider
export const RewardDisplayExample: React.FC = () => {
  return (
    <RewardDisplayProvider>
      <div className="p-4 space-y-4">
        <h2 className="text-xl font-bold">Reward Display Example</h2>
        <RewardController />
        <RewardImage />
      </div>
    </RewardDisplayProvider>
  );
};

export default RewardDisplayExample;
