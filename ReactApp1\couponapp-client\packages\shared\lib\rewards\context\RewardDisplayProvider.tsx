import React, { createContext, useContext, ReactNode } from 'react';
import { RewardRoll } from '../types';

interface RewardDisplayContextValue {
  rewardRoll: RewardRoll | null;
}

interface RewardDisplayProviderProps {
  children: ReactNode;
  rewardRoll: RewardRoll | null;
}

const RewardDisplayContext = createContext<RewardDisplayContextValue | null>(null);

export const RewardDisplayProvider: React.FC<RewardDisplayProviderProps> = ({
  children,
  rewardRoll
}) => {
  const value: RewardDisplayContextValue = {
    rewardRoll
  };

  return (
    <RewardDisplayContext.Provider value={value}>
      {children}
    </RewardDisplayContext.Provider>
  );
};

export const useRewardDisplay = (): RewardDisplayContextValue => {
  const context = useContext(RewardDisplayContext);
  
  if (!context) {
    throw new Error('useRewardDisplay must be used within a RewardDisplayProvider');
  }
  
  return context;
};
