import React, { createContext, useContext, ReactNode, useState } from 'react';
import { RewardDefinition } from '../types';
import { AssetUrl } from '../../types/widgetSettings';

// Generic reward interface that can accommodate both RewardDefinition and CampaignReward
export interface DisplayableReward {
  id: string;
  name?: string;
  description?: string;
  image?: AssetUrl;
  type?: string;
}

interface RewardDisplayContextValue {
  currentReward: DisplayableReward | null;
  setCurrentReward: (reward: DisplayableReward | null) => void;
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
  clearReward: () => void;
}

interface RewardDisplayProviderProps {
  children: ReactNode;
  initialReward?: DisplayableReward | null;
  initialVisible?: boolean;
}

const RewardDisplayContext = createContext<RewardDisplayContextValue | null>(null);

export const RewardDisplayProvider: React.FC<RewardDisplayProviderProps> = ({
  children,
  initialReward = null,
  initialVisible = false
}) => {
  const [currentReward, setCurrentReward] = useState<DisplayableReward | null>(initialReward);
  const [isVisible, setIsVisible] = useState<boolean>(initialVisible);

  const clearReward = () => {
    setCurrentReward(null);
    setIsVisible(false);
  };

  const value: RewardDisplayContextValue = {
    currentReward,
    setCurrentReward,
    isVisible,
    setIsVisible,
    clearReward
  };

  return (
    <RewardDisplayContext.Provider value={value}>
      {children}
    </RewardDisplayContext.Provider>
  );
};

export const useRewardDisplay = (): RewardDisplayContextValue => {
  const context = useContext(RewardDisplayContext);
  
  if (!context) {
    throw new Error('useRewardDisplay must be used within a RewardDisplayProvider');
  }
  
  return context;
};
