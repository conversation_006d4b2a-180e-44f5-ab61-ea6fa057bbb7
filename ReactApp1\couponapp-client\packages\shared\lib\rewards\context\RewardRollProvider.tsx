import React, { createContext, useContext, ReactNode } from 'react';
import { RewardRoll } from '../types';

interface RewardRollContextValue {
  rewardRoll: RewardRoll | null;
}

interface RewardRollProviderProps {
  children: ReactNode;
  rewardRoll: RewardRoll | null;
}

const RewardRollContext = createContext<RewardRollContextValue | null>(null);

export const RewardRollProvider: React.FC<RewardRollProviderProps> = ({
  children,
  rewardRoll
}) => {
  const value: RewardRollContextValue = {
    rewardRoll
  };

  return (
    <RewardRollContext.Provider value={value}>
      {children}
    </RewardRollContext.Provider>
  );
};

export const useRewardRoll = (): RewardRollContextValue => {
  const context = useContext(RewardRollContext);
  
  if (!context) {
    throw new Error('useRewardRoll must be used within a RewardRollProvider');
  }
  
  return context;
};
