import { useState, useEffect, useCallback } from 'react';
import { useRewards } from '../context/RewardsProvider';
import { RewardRoll } from '../types';
import { addRewardRollUpdateListener } from '../RewardsManager';

/**
 * Hook to fetch a reward roll result for a specific round ID using the existing rewards system
 * @param roundId - The ID of the round to fetch the reward for
 * @param enabled - Whether the query should be enabled (default: true when roundId is provided)
 * @returns Query result containing the reward roll data
 */
export function useRollResult(roundId: string | null, enabled: boolean = true) {
  const { getPickResultByRoundId, loadingState } = useRewards();
  const [rewardRollResult, setRewardRollResult] = useState<RewardRoll | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(true);


  useEffect(() => {
    console.log("roundId changed:", roundId)
  }, [roundId])

  const fetchReward = useCallback(async () => {

    console.log("roundId, enabled:", roundId, enabled)

    if(!enabled) {
      setIsLoading(false)
      return;
    }
    if (!roundId) {
      setRewardRollResult(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const result = await getPickResultByRoundId(roundId);
      setRewardRollResult(result);
      console.log("Reward roll result:", roundId, result)
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch reward'));
      setRewardRollResult(null);
    } finally {
      setIsLoading(false);
    }
  }, [roundId, enabled]);

  useEffect(() => {
    console.log("New effect shit")
    fetchReward();
  }, [fetchReward]);

  // Listen for reward roll updates to automatically refresh when a reward is finished rolling
  useEffect(() => {
    if (!roundId || !enabled) return;

    const cleanup = addRewardRollUpdateListener((updatedRewardRoll) => {
      console.log("Reward roll updated:", updatedRewardRoll)
      if (updatedRewardRoll.roundId === roundId) {
        setRewardRollResult(updatedRewardRoll);
        console.log("Reward roll updated via listener:", roundId, updatedRewardRoll);
      }
    });

    return cleanup;
  }, [roundId, enabled]);

  return {
    rewardRollResult,
    isRewardLoading: isLoading || (loadingState.isLoading && loadingState.operation === 'fetching'),
    error,
    refetch: fetchReward,
    // Convenience properties
    hasReward: !!rewardRollResult?.result?.reward,
    hasWon: !!rewardRollResult?.result?.hasWon,
    isCompleted: rewardRollResult?.status === 'completed',
    isPending: rewardRollResult?.status === 'pending',
    isRolling: rewardRollResult?.status === 'rolling',
    isFailed: rewardRollResult?.status === 'failed',
  };
}